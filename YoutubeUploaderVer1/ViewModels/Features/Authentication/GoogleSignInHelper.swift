//
//  GoogleSignInHelper.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 03/04/25.
//

import Foundation
import GoogleSignIn
import SwiftUI

class GoogleSignInHelper: ObservableObject {
    @Published var user: GIDGoogleUser?
    @Published var accessToken: String?
    @Published var isSignedIn: Bool = false
    static let shared = GoogleSignInHelper()

    private var refreshTimer: Timer?
    private var isRefreshingToken = false
    private let appGroupIdentifier = "group.com.codecraft.YouTube-Share"
    
    init() {
        restorePreviousSignIn()
    }

    //sign in fucntion
    func signIn() {
        let scopes = [
            "https://www.googleapis.com/auth/youtube.readonly",
            "https://www.googleapis.com/auth/youtube",
            "https://www.googleapis.com/auth/youtube.force-ssl",
            "https://www.googleapis.com/auth/youtube.upload",
            "https://www.googleapis.com/auth/yt-analytics.readonly",
            "https://www.googleapis.com/auth/yt-analytics-monetary.readonly",
        ]

        GIDSignIn.sharedInstance.signIn(withPresenting: NSApplication.shared.keyWindow!,hint: nil, additionalScopes: scopes) { signInResult, error in
            if let error = error {
                print("Google Sign-In error: \(error.localizedDescription)")
                return
            }

            DispatchQueue.main.async {
                self.user = signInResult?.user
                self.accessToken = signInResult?.user.accessToken.tokenString

                self.isSignedIn = true
                print("Signed in as: \(self.user?.profile?.name ?? "Unknown")")
                print("Access Token: \(self.accessToken ?? "No Token")")

                // Store access token in shared UserDefaults for widget access
                self.storeAccessTokenInSharedDefaults(self.accessToken)
            }
        }
    }

    //sign out fucntion
    func signOut() {
        DispatchQueue.global(qos: .background).async {
            GIDSignIn.sharedInstance.signOut()
            
            DispatchQueue.main.async {
                self.user = nil
                self.accessToken = nil
                self.isSignedIn = false

                // Clear access token from shared UserDefaults
                self.clearAccessTokenFromSharedDefaults()
            }
        }
    }

    //restore already signed in user
    private func restorePreviousSignIn() {
        GIDSignIn.sharedInstance.restorePreviousSignIn { [weak self] user, error in
            if let user = user {
                self?.user = user
                self?.accessToken = user.accessToken.tokenString
                self?.isSignedIn = true
            } else {
                print("No previous sign-in found.")
            }
        }
    }

    
    //this is awaitable version of getvalid accessToekn
    func getValidAccessToken() async -> String? {
            guard let currentUser = GIDSignIn.sharedInstance.currentUser else {
                print("User not signed in.")
                return nil
            }

            return await withCheckedContinuation { continuation in
                currentUser.refreshTokensIfNeeded { [weak self] user, error in
                    if let error = error {
                        print("Failed to refresh token: \(error.localizedDescription)")
                        continuation.resume(returning: nil)
                        return
                    }

                    guard let refreshedToken = user?.accessToken.tokenString else {
                        print("No refreshed token available.")
                        continuation.resume(returning: nil)
                        return
                    }

                    // Safe to access self on MainActor
                    self?.accessToken = refreshedToken

                    // Store refreshed token in shared UserDefaults for widget access
                    self?.storeAccessTokenInSharedDefaults(refreshedToken)

                    continuation.resume(returning: refreshedToken)
                }
            }
        }
    
    //get the new accessToken
    private func refreshAccessToken() {
        guard let user = GIDSignIn.sharedInstance.currentUser else {
            print("No signed-in user found.")
            return
        }
        
        
        
        user.refreshTokensIfNeeded { [weak self] user, error in
            if let error = error {
                print("Error refreshing token: \(error.localizedDescription)")
                return
            }
                        
            DispatchQueue.main.async {
                self?.accessToken = user?.accessToken.tokenString
                print("Refreshed Access Token: \(self?.accessToken ?? "No Token")")

                // Store refreshed token in shared UserDefaults for widget access
                self?.storeAccessTokenInSharedDefaults(self?.accessToken)
            }
        }
    }

    // MARK: - Shared UserDefaults Methods for Widget Access
    private func storeAccessTokenInSharedDefaults(_ token: String?) {
        guard let token = token else { return }

        if let sharedDefaults = UserDefaults(suiteName: appGroupIdentifier) {
            sharedDefaults.set(token, forKey: "youtube_access_token")
            sharedDefaults.synchronize()
            print("Stored access token in shared UserDefaults for widget access")
        }
    }

    private func clearAccessTokenFromSharedDefaults() {
        if let sharedDefaults = UserDefaults(suiteName: appGroupIdentifier) {
            sharedDefaults.removeObject(forKey: "youtube_access_token")
            sharedDefaults.synchronize()
            print("Cleared access token from shared UserDefaults")
        }
    }
}
