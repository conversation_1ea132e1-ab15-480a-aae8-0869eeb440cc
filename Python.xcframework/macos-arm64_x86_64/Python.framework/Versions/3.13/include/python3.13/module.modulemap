module Python {
    umbrella header "Python.h"
    export *
    link "Python"

    exclude header "datetime.h"
    exclude header "dynamic_annotations.h"
    exclude header "errcode.h"
    exclude header "frameobject.h"
    exclude header "marshal.h"
    exclude header "opcode_ids.h"
    exclude header "opcode.h"
    exclude header "osdefs.h"
    exclude header "py_curses.h"
    exclude header "pyconfig-arm32_64.h"
    exclude header "pyconfig-arm64.h"
    exclude header "pyconfig-x86_64.h"
    exclude header "pydtrace.h"
    exclude header "pyexpat.h"
    exclude header "structmember.h"

    exclude header "cpython/abstract.h"
    exclude header "cpython/bytearrayobject.h"
    exclude header "cpython/bytesobject.h"
    exclude header "cpython/cellobject.h"
    exclude header "cpython/ceval.h"
    exclude header "cpython/classobject.h"
    exclude header "cpython/code.h"
    exclude header "cpython/compile.h"
    exclude header "cpython/complexobject.h"
    exclude header "cpython/context.h"
    exclude header "cpython/critical_section.h"
    exclude header "cpython/descrobject.h"
    exclude header "cpython/dictobject.h"
    exclude header "cpython/fileobject.h"
    exclude header "cpython/fileutils.h"
    exclude header "cpython/floatobject.h"
    exclude header "cpython/frameobject.h"
    exclude header "cpython/funcobject.h"
    exclude header "cpython/genobject.h"
    exclude header "cpython/import.h"
    exclude header "cpython/initconfig.h"
    exclude header "cpython/listobject.h"
    exclude header "cpython/lock.h"
    exclude header "cpython/longintrepr.h"
    exclude header "cpython/longobject.h"
    exclude header "cpython/memoryobject.h"
    exclude header "cpython/methodobject.h"
    exclude header "cpython/modsupport.h"
    exclude header "cpython/monitoring.h"
    exclude header "cpython/object.h"
    exclude header "cpython/objimpl.h"
    exclude header "cpython/odictobject.h"
    exclude header "cpython/picklebufobject.h"
    exclude header "cpython/pthread_stubs.h"
    exclude header "cpython/pyatomic.h"
    exclude header "cpython/pyatomic_gcc.h"
    exclude header "cpython/pyatomic_msc.h"
    exclude header "cpython/pyatomic_std.h"
    exclude header "cpython/pyctype.h"
    exclude header "cpython/pydebug.h"
    exclude header "cpython/pyerrors.h"
    exclude header "cpython/pyfpe.h"
    exclude header "cpython/pyframe.h"
    exclude header "cpython/pyhash.h"
    exclude header "cpython/pylifecycle.h"
    exclude header "cpython/pymem.h"
    exclude header "cpython/pystate.h"
    exclude header "cpython/pystats.h"
    exclude header "cpython/pythonrun.h"
    exclude header "cpython/pythread.h"
    exclude header "cpython/pytime.h"
    exclude header "cpython/setobject.h"
    exclude header "cpython/sysmodule.h"
    exclude header "cpython/traceback.h"
    exclude header "cpython/tracemalloc.h"
    exclude header "cpython/tupleobject.h"
    exclude header "cpython/unicodeobject.h"
    exclude header "cpython/warnings.h"
    exclude header "cpython/weakrefobject.h"

    exclude header "internal/mimalloc/mimalloc.h"
    exclude header "internal/mimalloc/mimalloc/atomic.h"
    exclude header "internal/mimalloc/mimalloc/internal.h"
    exclude header "internal/mimalloc/mimalloc/prim.h"
    exclude header "internal/mimalloc/mimalloc/track.h"
    exclude header "internal/mimalloc/mimalloc/types.h"
    exclude header "internal/pycore_abstract.h"
    exclude header "internal/pycore_asdl.h"
    exclude header "internal/pycore_ast.h"
    exclude header "internal/pycore_ast_state.h"
    exclude header "internal/pycore_atexit.h"
    exclude header "internal/pycore_backoff.h"
    exclude header "internal/pycore_bitutils.h"
    exclude header "internal/pycore_blocks_output_buffer.h"
    exclude header "internal/pycore_brc.h"
    exclude header "internal/pycore_bytes_methods.h"
    exclude header "internal/pycore_bytesobject.h"
    exclude header "internal/pycore_call.h"
    exclude header "internal/pycore_capsule.h"
    exclude header "internal/pycore_cell.h"
    exclude header "internal/pycore_ceval.h"
    exclude header "internal/pycore_ceval_state.h"
    exclude header "internal/pycore_code.h"
    exclude header "internal/pycore_codecs.h"
    exclude header "internal/pycore_compile.h"
    exclude header "internal/pycore_complexobject.h"
    exclude header "internal/pycore_condvar.h"
    exclude header "internal/pycore_context.h"
    exclude header "internal/pycore_critical_section.h"
    exclude header "internal/pycore_crossinterp.h"
    exclude header "internal/pycore_descrobject.h"
    exclude header "internal/pycore_dict.h"
    exclude header "internal/pycore_dict_state.h"
    exclude header "internal/pycore_dtoa.h"
    exclude header "internal/pycore_emscripten_signal.h"
    exclude header "internal/pycore_emscripten_trampoline.h"
    exclude header "internal/pycore_exceptions.h"
    exclude header "internal/pycore_faulthandler.h"
    exclude header "internal/pycore_fileutils.h"
    exclude header "internal/pycore_fileutils_windows.h"
    exclude header "internal/pycore_floatobject.h"
    exclude header "internal/pycore_flowgraph.h"
    exclude header "internal/pycore_format.h"
    exclude header "internal/pycore_frame.h"
    exclude header "internal/pycore_freelist.h"
    exclude header "internal/pycore_function.h"
    exclude header "internal/pycore_gc.h"
    exclude header "internal/pycore_genobject.h"
    exclude header "internal/pycore_getopt.h"
    exclude header "internal/pycore_gil.h"
    exclude header "internal/pycore_global_objects.h"
    exclude header "internal/pycore_global_objects_fini_generated.h"
    exclude header "internal/pycore_global_strings.h"
    exclude header "internal/pycore_hamt.h"
    exclude header "internal/pycore_hashtable.h"
    exclude header "internal/pycore_identifier.h"
    exclude header "internal/pycore_import.h"
    exclude header "internal/pycore_importdl.h"
    exclude header "internal/pycore_initconfig.h"
    exclude header "internal/pycore_instruction_sequence.h"
    exclude header "internal/pycore_instruments.h"
    exclude header "internal/pycore_interp.h"
    exclude header "internal/pycore_intrinsics.h"
    exclude header "internal/pycore_jit.h"
    exclude header "internal/pycore_list.h"
    exclude header "internal/pycore_llist.h"
    exclude header "internal/pycore_lock.h"
    exclude header "internal/pycore_long.h"
    exclude header "internal/pycore_memoryobject.h"
    exclude header "internal/pycore_mimalloc.h"
    exclude header "internal/pycore_modsupport.h"
    exclude header "internal/pycore_moduleobject.h"
    exclude header "internal/pycore_namespace.h"
    exclude header "internal/pycore_object.h"
    exclude header "internal/pycore_object_alloc.h"
    exclude header "internal/pycore_object_stack.h"
    exclude header "internal/pycore_object_state.h"
    exclude header "internal/pycore_obmalloc.h"
    exclude header "internal/pycore_obmalloc_init.h"
    exclude header "internal/pycore_opcode_metadata.h"
    exclude header "internal/pycore_opcode_utils.h"
    exclude header "internal/pycore_optimizer.h"
    exclude header "internal/pycore_parking_lot.h"
    exclude header "internal/pycore_parser.h"
    exclude header "internal/pycore_pathconfig.h"
    exclude header "internal/pycore_pyarena.h"
    exclude header "internal/pycore_pyatomic_ft_wrappers.h"
    exclude header "internal/pycore_pybuffer.h"
    exclude header "internal/pycore_pyerrors.h"
    exclude header "internal/pycore_pyhash.h"
    exclude header "internal/pycore_pylifecycle.h"
    exclude header "internal/pycore_pymath.h"
    exclude header "internal/pycore_pymem.h"
    exclude header "internal/pycore_pymem_init.h"
    exclude header "internal/pycore_pystate.h"
    exclude header "internal/pycore_pystats.h"
    exclude header "internal/pycore_pythonrun.h"
    exclude header "internal/pycore_pythread.h"
    exclude header "internal/pycore_qsbr.h"
    exclude header "internal/pycore_range.h"
    exclude header "internal/pycore_runtime.h"
    exclude header "internal/pycore_runtime_init.h"
    exclude header "internal/pycore_runtime_init_generated.h"
    exclude header "internal/pycore_semaphore.h"
    exclude header "internal/pycore_setobject.h"
    exclude header "internal/pycore_signal.h"
    exclude header "internal/pycore_sliceobject.h"
    exclude header "internal/pycore_stackref.h"
    exclude header "internal/pycore_strhex.h"
    exclude header "internal/pycore_structseq.h"
    exclude header "internal/pycore_symtable.h"
    exclude header "internal/pycore_sysmodule.h"
    exclude header "internal/pycore_time.h"
    exclude header "internal/pycore_token.h"
    exclude header "internal/pycore_traceback.h"
    exclude header "internal/pycore_tracemalloc.h"
    exclude header "internal/pycore_tstate.h"
    exclude header "internal/pycore_tuple.h"
    exclude header "internal/pycore_typeobject.h"
    exclude header "internal/pycore_typevarobject.h"
    exclude header "internal/pycore_ucnhash.h"
    exclude header "internal/pycore_unicodeobject.h"
    exclude header "internal/pycore_unicodeobject_generated.h"
    exclude header "internal/pycore_unionobject.h"
    exclude header "internal/pycore_uop_ids.h"
    exclude header "internal/pycore_uop_metadata.h"
    exclude header "internal/pycore_warnings.h"
    exclude header "internal/pycore_weakref.h"

}
