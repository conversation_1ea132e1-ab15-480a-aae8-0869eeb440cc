# Config file for running mypy on _pyrepl.
# Run mypy by invoking `mypy --config-file Lib/_pyrepl/mypy.ini`
# on the command-line from the repo root

[mypy]
files = Lib/_pyrepl
mypy_path = $MYPY_CONFIG_FILE_DIR/../../Misc/mypy
explicit_package_bases = True
python_version = 3.13
platform = linux
pretty = True

# Enable most stricter settings
enable_error_code = ignore-without-code,redundant-expr
strict = True

# Various stricter settings that we can't yet enable
# Try to enable these in the following order:
disallow_untyped_calls = False
disallow_untyped_defs = False
check_untyped_defs = False

# Various internal modules that typeshed deliberately doesn't have stubs for:
[mypy-_abc.*,_opcode.*,_overlapped.*,_testcapi.*,_testinternalcapi.*,test.*]
ignore_missing_imports = True

# Other untyped parts of the stdlib
[mypy-idlelib.*]
ignore_missing_imports = True
