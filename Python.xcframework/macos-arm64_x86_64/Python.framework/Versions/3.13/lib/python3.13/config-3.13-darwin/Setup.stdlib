# -*- makefile -*-
#
# This file is autogenerated from Modules/Setup.stdlib.in.
#

# The file is not used by default yet. For testing do:
#
#     ln -sfr Modules/Setup.stdlib Modules/Setup.local
#

# * @MODULE_{NAME}_TRUE@ is removed when configure detects all build
#   dependencies for a module. Otherwise the template variable is replaced
#   by a comment "#" and the module is skipped.
# * Module lines without any compiler and linker flags automatically use
#   $(MODULE_{NAME}_CFLAGS) and $(MODULE_{NAME}_LDFLAGS). These flags are
#   generated by PY_STDLIB_MOD macro.
# * All source files automatically depend on $(PYTHON_HEADERS) and
#   $(MODULE_{NAME}_DEPS).
#
# See Modules/Setup and Modules/makesetup
#


# Build modules statically or as shared extensions
# *shared* / *static*
*shared*


############################################################################
# Modules that should always be present (POSIX and Windows):
array arraymodule.c
_asyncio _asynciomodule.c
_bisect _bisectmodule.c
_contextvars _contextvarsmodule.c
_csv _csv.c
_heapq _heapqmodule.c
_json _json.c
_lsprof _lsprof.c rotatingtree.c
_opcode _opcode.c
_pickle _pickle.c
_queue _queuemodule.c
_random _randommodule.c
_struct _struct.c

# build supports subinterpreters
_interpreters _interpretersmodule.c
_interpchannels _interpchannelsmodule.c
_interpqueues _interpqueuesmodule.c

_zoneinfo _zoneinfo.c

# needs libm
math mathmodule.c
cmath cmathmodule.c
_statistics _statisticsmodule.c

# needs libm and on some platforms librt
_datetime _datetimemodule.c

# _decimal uses libmpdec
# either static libmpdec.a from Modules/_decimal/libmpdec or libmpdec.so
# with ./configure --with-system-libmpdec
_decimal _decimal/_decimal.c

# compression libs and binascii (optional CRC32 from zlib)
# bindings need -lbz2, -lz, or -llzma, respectively
binascii binascii.c
_bz2 _bz2module.c
_lzma _lzmamodule.c
zlib zlibmodule.c

# dbm/gdbm
# dbm needs either libndbm, libgdbm_compat, or libdb 5.x
_dbm _dbmmodule.c
# gdbm module needs -lgdbm
#_gdbm _gdbmmodule.c

# needs -lreadline or -ledit, sometimes termcap, termlib, or tinfo
readline readline.c

# hashing builtins, can be disabled with --without-builtin-hashlib-hashes
_md5 md5module.c -I$(srcdir)/Modules/_hacl/include _hacl/Hacl_Hash_MD5.c -D_BSD_SOURCE -D_DEFAULT_SOURCE
_sha1 sha1module.c -I$(srcdir)/Modules/_hacl/include _hacl/Hacl_Hash_SHA1.c -D_BSD_SOURCE -D_DEFAULT_SOURCE
_sha2 sha2module.c -I$(srcdir)/Modules/_hacl/include Modules/_hacl/libHacl_Hash_SHA2.a
_sha3 sha3module.c -I$(srcdir)/Modules/_hacl/include _hacl/Hacl_Hash_SHA3.c -D_BSD_SOURCE -D_DEFAULT_SOURCE
_blake2 _blake2/blake2module.c _blake2/blake2b_impl.c _blake2/blake2s_impl.c

############################################################################
# XML and text

# pyexpat module uses libexpat
# either static libexpat.a from Modules/expat or libexpat.so with
# ./configure --with-system-expat
pyexpat pyexpat.c

# _elementtree libexpat via CAPI hook in pyexpat.
_elementtree _elementtree.c

_codecs_cn cjkcodecs/_codecs_cn.c
_codecs_hk cjkcodecs/_codecs_hk.c
_codecs_iso2022 cjkcodecs/_codecs_iso2022.c
_codecs_jp cjkcodecs/_codecs_jp.c
_codecs_kr cjkcodecs/_codecs_kr.c
_codecs_tw cjkcodecs/_codecs_tw.c
_multibytecodec cjkcodecs/multibytecodec.c
unicodedata unicodedata.c

############################################################################
# Modules with some UNIX dependencies
#

fcntl fcntlmodule.c
grp grpmodule.c
mmap mmapmodule.c
# needs sys/soundcard.h or linux/soundcard.h (Linux, FreeBSD)
_posixsubprocess _posixsubprocess.c
resource resource.c
select selectmodule.c
_socket socketmodule.c
syslog syslogmodule.c
termios termios.c

# multiprocessing
_posixshmem _multiprocessing/posixshmem.c
_multiprocessing _multiprocessing/multiprocessing.c _multiprocessing/semaphore.c


############################################################################
# Modules with third party dependencies
#

# needs -lffi and -ldl
_ctypes _ctypes/_ctypes.c _ctypes/callbacks.c _ctypes/callproc.c _ctypes/stgdict.c _ctypes/cfield.c _ctypes/malloc_closure.c

# needs -lncurses[w], sometimes -ltermcap/tinfo
_curses _cursesmodule.c
# needs -lncurses[w] and -lpanel[w]
_curses_panel _curses_panel.c

_sqlite3 _sqlite/blob.c _sqlite/connection.c _sqlite/cursor.c _sqlite/microprotocols.c _sqlite/module.c _sqlite/prepare_protocol.c _sqlite/row.c _sqlite/statement.c _sqlite/util.c

# needs -lssl and -lcrypt
_ssl _ssl.c
# needs -lcrypt
_hashlib _hashopenssl.c

# Linux: -luuid, BSD/AIX: libc's uuid_create()
_uuid _uuidmodule.c

_tkinter _tkinter.c tkappinit.c

############################################################################
# macOS specific modules

# _scproxy needs SystemConfiguration and CoreFoundation framework
_scproxy _scproxy.c


############################################################################
# Test modules

xxsubtype xxsubtype.c
_xxtestfuzz _xxtestfuzz/_xxtestfuzz.c _xxtestfuzz/fuzzer.c
_testbuffer _testbuffer.c
_testinternalcapi _testinternalcapi.c _testinternalcapi/test_lock.c _testinternalcapi/pytime.c _testinternalcapi/set.c _testinternalcapi/test_critical_sections.c
_testcapi _testcapimodule.c _testcapi/vectorcall.c _testcapi/heaptype.c _testcapi/abstract.c _testcapi/unicode.c _testcapi/dict.c _testcapi/set.c _testcapi/list.c _testcapi/tuple.c _testcapi/getargs.c _testcapi/datetime.c _testcapi/docstring.c _testcapi/mem.c _testcapi/watchers.c _testcapi/long.c _testcapi/float.c _testcapi/complex.c _testcapi/numbers.c _testcapi/structmember.c _testcapi/exceptions.c _testcapi/code.c _testcapi/buffer.c _testcapi/pyatomic.c _testcapi/run.c _testcapi/file.c _testcapi/codec.c _testcapi/immortal.c _testcapi/gc.c _testcapi/hash.c _testcapi/time.c _testcapi/bytes.c _testcapi/object.c _testcapi/monitoring.c
_testlimitedcapi _testlimitedcapi.c _testlimitedcapi/abstract.c _testlimitedcapi/bytearray.c _testlimitedcapi/bytes.c _testlimitedcapi/complex.c _testlimitedcapi/dict.c _testlimitedcapi/eval.c _testlimitedcapi/float.c _testlimitedcapi/heaptype_relative.c _testlimitedcapi/import.c _testlimitedcapi/list.c _testlimitedcapi/long.c _testlimitedcapi/object.c _testlimitedcapi/pyos.c _testlimitedcapi/set.c _testlimitedcapi/sys.c _testlimitedcapi/tuple.c _testlimitedcapi/unicode.c _testlimitedcapi/vectorcall_limited.c _testlimitedcapi/file.c
_testclinic _testclinic.c
_testclinic_limited _testclinic_limited.c

# Some testing modules MUST be built as shared libraries.
*shared*
_testimportmultiple _testimportmultiple.c
_testmultiphase _testmultiphase.c
_testsinglephase _testsinglephase.c
_testexternalinspection _testexternalinspection.c
_ctypes_test _ctypes/_ctypes_test.c

# Limited API template modules; must be built as shared modules.
xxlimited xxlimited.c
xxlimited_35 xxlimited_35.c
