//
//  AnalyticsWidget.swift
//  AnalyticsWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> <PERSON> on 02/07/25.
//

import WidgetKit
import SwiftUI
import Charts

// MARK: - Extensions
extension Date {
    func formattedForYouTubeAPI() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: self)
    }
}

// MARK: - Simplified Models for Widget
struct SimpleVideoAnalytics {
    let videoId: String
    let views: Int
    let likes: Int
    let comments: Int
    let shares: Int
    let engagementRate: Double
}

struct SimpleYouTubeVideo {
    let title: String
    let description: String
    let publishedAt: String
    let thumbnailURL: String
    let videoId: String
}

struct SimpleGoogleSignInHelper {
    static let shared = SimpleGoogleSignInHelper()
    private let appGroupIdentifier = "group.com.codecraft.YouTube-Share"

    func getValidAccessToken() async -> String? {
        // Try to get access token from shared UserDefaults
        if let sharedDefaults = UserDefaults(suiteName: appGroupIdentifier) {
            return sharedDefaults.string(forKey: "youtube_access_token")
        }
        return nil
    }

    // This method would be called by the main app to store the token
    func storeAccessToken(_ token: String) {
        if let sharedDefaults = UserDefaults(suiteName: appGroupIdentifier) {
            sharedDefaults.set(token, forKey: "youtube_access_token")
        }
    }
}

// MARK: - Simplified API Service for Widget
class SimpleYouTubeAPIService {
    static let shared = SimpleYouTubeAPIService()

    private init() {}

    func fetchUploadsPlaylistId(accessToken: String) async -> String? {
        // Simplified implementation
        return nil
    }

    func fetchUploadedVideos(accessToken: String, playlistId: String) async -> (videos: [SimpleYouTubeVideo], nextPageToken: String?)? {
        // Simplified implementation
        return nil
    }

    func fetchVideoAnalytics(videoId: String, accessToken: String, startDate: String? = nil, endDate: String? = nil) async -> SimpleVideoAnalytics? {
        // Simplified implementation
        return nil
    }
}

// MARK: - Widget Data Models
struct WidgetAnalyticsData {
    let lastUploadedVideo: WidgetVideoData?
    let topPerformer: WidgetVideoData?
    let lastUpdated: Date
}

struct WidgetVideoData {
    let title: String
    let thumbnailURL: String
    let videoId: String
    let views: Int
    let likes: Int
    let comments: Int
    let trendingScore: Double?
    let publishedAt: String

    var formattedViews: String {
        formatNumber(views)
    }

    var formattedLikes: String {
        formatNumber(likes)
    }

    var formattedComments: String {
        formatNumber(comments)
    }

    private func formatNumber(_ number: Int) -> String {
        if number >= 1_000_000 {
            return String(format: "%.1fM", Double(number) / 1_000_000)
        } else if number >= 1_000 {
            return String(format: "%.1fK", Double(number) / 1_000)
        } else {
            return "\(number)"
        }
    }
}

// MARK: - Widget Entry
struct AnalyticsEntry: TimelineEntry {
    let date: Date
    let configuration: ConfigurationAppIntent
    let analyticsData: WidgetAnalyticsData?
    let isPlaceholder: Bool

    init(date: Date, configuration: ConfigurationAppIntent, analyticsData: WidgetAnalyticsData? = nil, isPlaceholder: Bool = false) {
        self.date = date
        self.configuration = configuration
        self.analyticsData = analyticsData
        self.isPlaceholder = isPlaceholder
    }
}

// MARK: - Widget Service
class WidgetAnalyticsService {
    static let shared = WidgetAnalyticsService()

    private init() {}

    func fetchAnalyticsData() async -> WidgetAnalyticsData? {
        // Get access token from shared authentication
        guard let accessToken = await getValidAccessToken() else {
            print("Widget: No valid access token available")
            return createMockData() // Return mock data for demo
        }

        // Fetch uploads playlist ID
        guard let playlistId = await SimpleYouTubeAPIService.shared.fetchUploadsPlaylistId(accessToken: accessToken) else {
            print("Widget: Failed to fetch uploads playlist ID")
            return createMockData() // Return mock data for demo
        }

        // Fetch last uploaded video
        let lastUploadedVideo = await fetchLastUploadedVideo(accessToken: accessToken, playlistId: playlistId)

        // Fetch top performer in last 7 days
        let topPerformer = await fetchTopPerformer(accessToken: accessToken, playlistId: playlistId)

        return WidgetAnalyticsData(
            lastUploadedVideo: lastUploadedVideo,
            topPerformer: topPerformer,
            lastUpdated: Date()
        )
    }

    private func getValidAccessToken() async -> String? {
        // Use the simplified authentication helper
        return await SimpleGoogleSignInHelper.shared.getValidAccessToken()
    }

    private func createMockData() -> WidgetAnalyticsData {
        let mockVideo = WidgetVideoData(
            title: "My Latest YouTube Video",
            thumbnailURL: "",
            videoId: "mock123",
            views: 1250,
            likes: 89,
            comments: 23,
            trendingScore: 1500.0,
            publishedAt: "2024-07-01T10:00:00Z"
        )

        let topVideo = WidgetVideoData(
            title: "Top Performing Video This Week",
            thumbnailURL: "",
            videoId: "top456",
            views: 5420,
            likes: 234,
            comments: 67,
            trendingScore: 3200.0,
            publishedAt: "2024-06-28T15:30:00Z"
        )

        return WidgetAnalyticsData(
            lastUploadedVideo: mockVideo,
            topPerformer: topVideo,
            lastUpdated: Date()
        )
    }

    private func fetchLastUploadedVideo(accessToken: String, playlistId: String) async -> WidgetVideoData? {
        guard let response = await SimpleYouTubeAPIService.shared.fetchUploadedVideos(accessToken: accessToken, playlistId: playlistId) else {
            return nil
        }

        let (videos, _) = response
        guard let lastVideo = videos.first else { return nil }

        // Fetch analytics for the last video
        guard let analytics = await SimpleYouTubeAPIService.shared.fetchVideoAnalytics(videoId: lastVideo.videoId, accessToken: accessToken) else {
            return WidgetVideoData(
                title: lastVideo.title,
                thumbnailURL: lastVideo.thumbnailURL,
                videoId: lastVideo.videoId,
                views: 0,
                likes: 0,
                comments: 0,
                trendingScore: nil,
                publishedAt: lastVideo.publishedAt
            )
        }

        return WidgetVideoData(
            title: lastVideo.title,
            thumbnailURL: lastVideo.thumbnailURL,
            videoId: lastVideo.videoId,
            views: analytics.views,
            likes: analytics.likes,
            comments: analytics.comments,
            trendingScore: nil,
            publishedAt: lastVideo.publishedAt
        )
    }

    private func fetchTopPerformer(accessToken: String, playlistId: String) async -> WidgetVideoData? {
        // Fetch recent videos (last 10)
        guard let response = await SimpleYouTubeAPIService.shared.fetchUploadedVideos(accessToken: accessToken, playlistId: playlistId) else {
            return nil
        }

        let (videos, _) = response
        var topVideo: (video: SimpleYouTubeVideo, analytics: SimpleVideoAnalytics, score: Double)?

        // Calculate trending scores for recent videos
        for video in videos.prefix(10) {
            if let analytics = await fetchLast7DaysAnalytics(videoId: video.videoId, accessToken: accessToken) {
                let trendingScore = calculateTrendingScore(analytics: analytics)

                if topVideo == nil || trendingScore > topVideo!.score {
                    topVideo = (video, analytics, trendingScore)
                }
            }
        }

        guard let top = topVideo else { return nil }

        return WidgetVideoData(
            title: top.video.title,
            thumbnailURL: top.video.thumbnailURL,
            videoId: top.video.videoId,
            views: top.analytics.views,
            likes: top.analytics.likes,
            comments: top.analytics.comments,
            trendingScore: top.score,
            publishedAt: top.video.publishedAt
        )
    }

    private func fetchLast7DaysAnalytics(videoId: String, accessToken: String) async -> SimpleVideoAnalytics? {
        let calendar = Calendar.current
        let endDate = Date()
        let startDate = calendar.date(byAdding: .day, value: -7, to: endDate) ?? endDate

        let startString = startDate.formattedForYouTubeAPI()
        let endString = endDate.formattedForYouTubeAPI()

        return await SimpleYouTubeAPIService.shared.fetchVideoAnalytics(
            videoId: videoId,
            accessToken: accessToken,
            startDate: startString,
            endDate: endString
        )
    }

    private func calculateTrendingScore(analytics: SimpleVideoAnalytics) -> Double {
        // Same formula as in the main app
        return Double(analytics.views) * 0.5 + Double(analytics.likes) * 0.3 + Double(analytics.comments) * 0.2
    }
}

// MARK: - Widget Provider
struct AnalyticsProvider: AppIntentTimelineProvider {
    func placeholder(in context: Context) -> AnalyticsEntry {
        AnalyticsEntry(
            date: Date(),
            configuration: ConfigurationAppIntent(),
            analyticsData: createPlaceholderData(),
            isPlaceholder: true
        )
    }

    func snapshot(for configuration: ConfigurationAppIntent, in context: Context) async -> AnalyticsEntry {
        if context.isPreview {
            return AnalyticsEntry(
                date: Date(),
                configuration: configuration,
                analyticsData: createPlaceholderData(),
                isPlaceholder: true
            )
        }

        let analyticsData = await WidgetAnalyticsService.shared.fetchAnalyticsData()
        return AnalyticsEntry(
            date: Date(),
            configuration: configuration,
            analyticsData: analyticsData
        )
    }

    func timeline(for configuration: ConfigurationAppIntent, in context: Context) async -> Timeline<AnalyticsEntry> {
        let currentDate = Date()
        let analyticsData = await WidgetAnalyticsService.shared.fetchAnalyticsData()

        let entry = AnalyticsEntry(
            date: currentDate,
            configuration: configuration,
            analyticsData: analyticsData
        )

        // Refresh every 30 minutes
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 30, to: currentDate) ?? currentDate
        return Timeline(entries: [entry], policy: .after(nextUpdate))
    }

    private func createPlaceholderData() -> WidgetAnalyticsData {
        let placeholderVideo = WidgetVideoData(
            title: "My Latest Video Title",
            thumbnailURL: "",
            videoId: "placeholder",
            views: 1234,
            likes: 89,
            comments: 23,
            trendingScore: 1500.0,
            publishedAt: "2024-07-01T10:00:00Z"
        )

        return WidgetAnalyticsData(
            lastUploadedVideo: placeholderVideo,
            topPerformer: placeholderVideo,
            lastUpdated: Date()
        )
    }
}

// MARK: - Widget Entry View
struct AnalyticsWidgetEntryView: View {
    var entry: AnalyticsProvider.Entry

    var body: some View {
        VStack(spacing: 0) {
            if let data = entry.analyticsData {
                // First Row: Last Uploaded Video
                if let lastVideo = data.lastUploadedVideo {
                    LastUploadedVideoRow(video: lastVideo)
                        .padding(.horizontal, 12)
                        .padding(.top, 8)
                }

                Divider()
                    .padding(.horizontal, 8)

                // Second Row: Top Performer
                if let topVideo = data.topPerformer {
                    TopPerformerRow(video: topVideo)
                        .padding(.horizontal, 12)
                        .padding(.bottom, 8)
                }
            } else {
                // Loading or error state
                VStack {
                    Image(systemName: "chart.bar.fill")
                        .font(.title2)
                        .foregroundColor(.secondary)
                    Text("Loading Analytics...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .containerBackground(.fill.tertiary, for: .widget)
    }
}

// MARK: - Last Uploaded Video Row
struct LastUploadedVideoRow: View {
    let video: WidgetVideoData

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: "video.fill")
                    .foregroundColor(.blue)
                    .font(.caption)
                Text("Latest Upload")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                Spacer()
            }

            Text(video.title)
                .font(.caption)
                .fontWeight(.semibold)
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            HStack(spacing: 12) {
                StatItem(icon: "eye.fill", value: video.formattedViews, color: .blue)
                StatItem(icon: "heart.fill", value: video.formattedLikes, color: .red)
                StatItem(icon: "bubble.left.fill", value: video.formattedComments, color: .green)

                Spacer()

                // Mini chart for views trend
                MiniChart(data: generateSampleData(), color: .blue)
                    .frame(width: 30, height: 15)
            }
        }
        .padding(.vertical, 6)
    }
}

// MARK: - Top Performer Row
struct TopPerformerRow: View {
    let video: WidgetVideoData

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: "trophy.fill")
                    .foregroundColor(.orange)
                    .font(.caption)
                Text("Top Performer (7 days)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                Spacer()
                if let score = video.trendingScore {
                    Text("Score: \(Int(score))")
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                }
            }

            Text(video.title)
                .font(.caption)
                .fontWeight(.semibold)
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            HStack(spacing: 12) {
                StatItem(icon: "eye.fill", value: video.formattedViews, color: .blue)
                StatItem(icon: "heart.fill", value: video.formattedLikes, color: .red)
                StatItem(icon: "bubble.left.fill", value: video.formattedComments, color: .green)

                Spacer()

                // Mini chart for engagement trend
                MiniChart(data: generateSampleData(), color: .orange)
                    .frame(width: 30, height: 15)
            }
        }
        .padding(.vertical, 6)
    }
}

// MARK: - Supporting UI Components
struct StatItem: View {
    let icon: String
    let value: String
    let color: Color

    var body: some View {
        HStack(spacing: 2) {
            Image(systemName: icon)
                .font(.caption2)
                .foregroundColor(color)
            Text(value)
                .font(.caption2)
                .fontWeight(.medium)
        }
    }
}

struct MiniChart: View {
    let data: [Double]
    let color: Color

    var body: some View {
        Chart {
            ForEach(Array(data.enumerated()), id: \.offset) { index, value in
                LineMark(
                    x: .value("Index", index),
                    y: .value("Value", value)
                )
                .foregroundStyle(color)
                .lineStyle(StrokeStyle(lineWidth: 1.5))
            }
        }
        .chartXAxis(.hidden)
        .chartYAxis(.hidden)
        .chartLegend(.hidden)
    }
}

// MARK: - Helper Functions
private func generateSampleData() -> [Double] {
    // Generate sample trending data for the mini charts
    return [10, 15, 12, 18, 25, 22, 30]
}

// MARK: - Widget Configuration
struct AnalyticsWidget: Widget {
    let kind: String = "AnalyticsWidget"

    var body: some WidgetConfiguration {
        AppIntentConfiguration(kind: kind, intent: ConfigurationAppIntent.self, provider: AnalyticsProvider()) { entry in
            AnalyticsWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("YouTube Analytics")
        .description("View your latest video stats and top performer.")
        .supportedFamilies([.systemMedium])
    }
}
