//
//  AppIntent.swift
//  AnalyticsWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 02/07/25.
//

import WidgetKit
import AppIntents

struct ConfigurationAppIntent: WidgetConfigurationIntent {
    static var title: LocalizedStringResource { "Analytics Configuration" }
    static var description: IntentDescription { "Configure your YouTube Analytics widget." }

    // Configuration parameter for refresh interval
    @Parameter(title: "Auto Refresh", default: true)
    var autoRefresh: Bool
}
